import React, { createContext, useContext, useState, useCallback } from 'react';
import { LogEntry } from '../components/LogConsole';

interface LogContextType {
  logs: LogEntry[];
  addLog: (level: LogEntry['level'], category: string, message: string, data?: any) => void;
  clearLogs: () => void;
}

const LogContext = createContext<LogContextType | null>(null);

export const useLog = () => {
  const context = useContext(LogContext);
  if (!context) {
    throw new Error('useLog must be used within a LogProvider');
  }
  return context;
};

export const LogProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);

  const addLog = useCallback((level: LogEntry['level'], category: string, message: string, data?: any) => {
    const newLog: LogEntry = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      level,
      category,
      message,
      data
    };

    setLogs(prev => {
      // Keep only the last 500 logs to prevent memory issues
      const newLogs = [...prev, newLog];
      return newLogs.length > 500 ? newLogs.slice(-500) : newLogs;
    });

    // Also log to browser console for debugging
    const consoleMessage = `[${category}] ${message}`;
    switch (level) {
      case 'error':
        console.error(consoleMessage, data);
        break;
      case 'warn':
        console.warn(consoleMessage, data);
        break;
      case 'debug':
        console.debug(consoleMessage, data);
        break;
      case 'success':
      case 'info':
      default:
        console.log(consoleMessage, data);
        break;
    }
  }, []);

  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  return (
    <LogContext.Provider value={{ logs, addLog, clearLogs }}>
      {children}
    </LogContext.Provider>
  );
};
