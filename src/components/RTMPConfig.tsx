import React from 'react';
import { Play, Square, ExternalLink } from 'lucide-react';

interface RTMPConfigProps {
  config: { url: string; key: string };
  onChange: (config: { url: string; key: string }) => void;
  isStreaming: boolean;
  onStart: () => void;
  onStop: () => void;
}

export const RTMPConfig: React.FC<RTMPConfigProps> = ({
  config,
  onChange,
  isStreaming,
  onStart,
  onStop,
}) => {
  return (
    <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-4">
      <h3 className="text-lg font-bold text-white mb-4 flex items-center space-x-2">
        <ExternalLink className="w-5 h-5" />
        <span>RTMP Streaming</span>
      </h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            RTMP URL
          </label>
          <input
            type="text"
            value={config.url}
            onChange={(e) => onChange({ ...config, url: e.target.value })}
            placeholder="rtmp://live.twitch.tv/live"
            className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
            disabled={isStreaming}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Stream Key
          </label>
          <input
            type="password"
            value={config.key}
            onChange={(e) => onChange({ ...config, key: e.target.value })}
            placeholder="Your stream key"
            className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
            disabled={isStreaming}
          />
        </div>
        
        <div className="pt-2">
          {!isStreaming ? (
            <button
              onClick={onStart}
              disabled={!config.url || !config.key}
              className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Play className="w-5 h-5" />
              <span>Start RTMP Stream</span>
            </button>
          ) : (
            <button
              onClick={onStop}
              className="w-full bg-gray-600 hover:bg-gray-500 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              <Square className="w-5 h-5" />
              <span>Stop Stream</span>
            </button>
          )}
        </div>
        
        <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
          <p className="text-yellow-400 text-xs">
            <strong>Note:</strong> RTMP streaming requires FFmpeg server implementation. 
            Configure your streaming platform's RTMP endpoint above.
          </p>
        </div>
      </div>
    </div>
  );
};