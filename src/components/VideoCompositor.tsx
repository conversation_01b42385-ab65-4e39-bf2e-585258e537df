import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Monitor, Grid, Maximize2, Bug } from 'lucide-react';
import { useLog } from '../contexts/LogContext';

interface VideoCompositorProps {
  peers: Record<string, any>;
  localStream: MediaStream | null;
}

type LayoutMode = 'grid' | 'focus' | 'pip';

// WebGL shaders
const vertexShaderSource = `#version 300 es
in vec2 a_position;
in vec2 a_texCoord;
out vec2 v_texCoord;

void main() {
  gl_Position = vec4(a_position, 0.0, 1.0);
  v_texCoord = a_texCoord;
}`;

const fragmentShaderSource = `#version 300 es
precision mediump float;

in vec2 v_texCoord;
out vec4 fragColor;

uniform sampler2D u_texture;
uniform float u_alpha;
uniform vec2 u_resolution;
uniform vec4 u_viewport; // x, y, width, height in normalized coordinates
uniform vec3 u_borderColor;
uniform float u_borderWidth;

void main() {
  // Check if we're in the viewport
  vec2 normalizedCoord = gl_FragCoord.xy / u_resolution;

  if (normalizedCoord.x < u_viewport.x ||
      normalizedCoord.x > u_viewport.x + u_viewport.z ||
      normalizedCoord.y < u_viewport.y ||
      normalizedCoord.y > u_viewport.y + u_viewport.w) {
    fragColor = vec4(0.0, 0.0, 0.0, 1.0); // Black background
    return;
  }

  // Calculate texture coordinates within the viewport
  vec2 viewportCoord = (normalizedCoord - u_viewport.xy) / u_viewport.zw;

  // Flip Y coordinate to fix vertical inversion
  viewportCoord.y = 1.0 - viewportCoord.y;

  // Sample the texture
  vec4 texColor = texture(u_texture, viewportCoord);

  // Add border effect
  vec2 borderDist = min(viewportCoord, 1.0 - viewportCoord);
  float borderFactor = min(borderDist.x, borderDist.y);

  if (borderFactor < u_borderWidth) {
    float borderAlpha = smoothstep(0.0, u_borderWidth, borderFactor);
    fragColor = vec4(mix(u_borderColor, texColor.rgb, borderAlpha), texColor.a * u_alpha);
  } else {
    fragColor = vec4(texColor.rgb, texColor.a * u_alpha);
  }
}`;

export const VideoCompositor: React.FC<VideoCompositorProps> = ({ peers, localStream }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [layoutMode, setLayoutMode] = useState<LayoutMode>('grid');
  const [focusedPeer, setFocusedPeer] = useState<string>('');
  const videoRefs = useRef<Record<string, HTMLVideoElement>>({});
  const localVideoRef = useRef<HTMLVideoElement | null>(null);
  const glRef = useRef<WebGL2RenderingContext | null>(null);
  const programRef = useRef<WebGLProgram | null>(null);
  const texturesRef = useRef<Map<string, WebGLTexture>>(new Map());
  const { addLog } = useLog();

  // Debug function to log current state
  const debugVideoState = useCallback(() => {
    addLog('info', 'Debug', '=== VIDEO COMPOSITOR DEBUG STATE ===');
    addLog('info', 'Debug', `Total peers: ${Object.keys(peers).length}`);

    // Log peer details
    Object.entries(peers).forEach(([peerId, peer]) => {
      addLog('info', 'Debug', `Peer ${peerId}:`, {
        hasConnection: !!peer.connection,
        hasStream: !!peer.stream,
        streamId: peer.stream?.id,
        streamTracks: peer.stream?.getTracks().map((t: MediaStreamTrack) => ({ kind: t.kind, id: t.id })) || []
      });
    });

    // Log video element details
    addLog('info', 'Debug', `Video elements: ${Object.keys(videoRefs.current).length}`);
    Object.entries(videoRefs.current).forEach(([peerId, video]) => {
      addLog('info', 'Debug', `Video element ${peerId}:`, {
        readyState: video.readyState,
        dimensions: `${video.videoWidth}x${video.videoHeight}`,
        hasStream: !!video.srcObject,
        streamId: (video.srcObject as MediaStream)?.id,
        paused: video.paused,
        ended: video.ended,
        currentTime: video.currentTime
      });
    });

    // Log local video
    const localVideo = localVideoRef.current;
    if (localVideo) {
      addLog('info', 'Debug', 'Local video:', {
        readyState: localVideo.readyState,
        dimensions: `${localVideo.videoWidth}x${localVideo.videoHeight}`,
        hasStream: !!localVideo.srcObject,
        streamId: (localVideo.srcObject as MediaStream)?.id,
        paused: localVideo.paused,
        ended: localVideo.ended
      });
    } else {
      addLog('info', 'Debug', 'No local video element');
    }

    // Log WebGL state
    addLog('info', 'Debug', 'WebGL state:', {
      hasGL: !!glRef.current,
      hasProgram: !!programRef.current,
      textureCount: texturesRef.current.size,
      textureKeys: Array.from(texturesRef.current.keys())
    });

    addLog('info', 'Debug', '=== END DEBUG STATE ===');
  }, [peers, addLog]);

  // WebGL helper functions
  const createShader = useCallback((gl: WebGL2RenderingContext, type: number, source: string): WebGLShader | null => {
    const shader = gl.createShader(type);
    if (!shader) return null;

    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      addLog('error', 'WebGL', `Shader compilation error: ${gl.getShaderInfoLog(shader)}`);
      gl.deleteShader(shader);
      return null;
    }

    return shader;
  }, [addLog]);

  const createProgram = useCallback((gl: WebGL2RenderingContext): WebGLProgram | null => {
    const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);

    if (!vertexShader || !fragmentShader) return null;

    const program = gl.createProgram();
    if (!program) return null;

    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      addLog('error', 'WebGL', `Program linking error: ${gl.getProgramInfoLog(program)}`);
      gl.deleteProgram(program);
      return null;
    }

    // Clean up shaders
    gl.deleteShader(vertexShader);
    gl.deleteShader(fragmentShader);

    return program;
  }, [createShader, addLog]);

  const initWebGL = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return false;

    const gl = canvas.getContext('webgl2');
    if (!gl) {
      addLog('error', 'WebGL', 'WebGL 2 not supported');
      return false;
    }

    glRef.current = gl;

    const program = createProgram(gl);
    if (!program) return false;

    programRef.current = program;

    // Set up vertex buffer for a full-screen quad
    const positions = new Float32Array([
      -1, -1,  0, 0,  // bottom-left
       1, -1,  1, 0,  // bottom-right
      -1,  1,  0, 1,  // top-left
       1,  1,  1, 1,  // top-right
    ]);

    const positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

    // Set up vertex attributes
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');

    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 16, 0);

    gl.enableVertexAttribArray(texCoordLocation);
    gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 16, 8);

    addLog('success', 'WebGL', 'WebGL 2 context initialized successfully');
    return true;
  }, [createProgram, addLog]);

  const createVideoTexture = useCallback((gl: WebGL2RenderingContext, video: HTMLVideoElement): WebGLTexture | null => {
    const texture = gl.createTexture();
    if (!texture) return null;

    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);

    return texture;
  }, []);

  const updateVideoTexture = useCallback((gl: WebGL2RenderingContext, texture: WebGLTexture, video: HTMLVideoElement) => {
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, video);
  }, []);

  const renderVideo = useCallback((
    gl: WebGL2RenderingContext,
    program: WebGLProgram,
    texture: WebGLTexture,
    viewport: { x: number; y: number; width: number; height: number },
    borderColor: [number, number, number] = [0, 0.83, 1], // Blue border
    alpha: number = 1.0
  ) => {
    gl.useProgram(program);

    // Enable blending for proper alpha compositing
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

    // Set uniforms
    const textureLocation = gl.getUniformLocation(program, 'u_texture');
    const alphaLocation = gl.getUniformLocation(program, 'u_alpha');
    const resolutionLocation = gl.getUniformLocation(program, 'u_resolution');
    const viewportLocation = gl.getUniformLocation(program, 'u_viewport');
    const borderColorLocation = gl.getUniformLocation(program, 'u_borderColor');
    const borderWidthLocation = gl.getUniformLocation(program, 'u_borderWidth');

    if (textureLocation !== null) gl.uniform1i(textureLocation, 0);
    if (alphaLocation !== null) gl.uniform1f(alphaLocation, alpha);
    if (resolutionLocation !== null) gl.uniform2f(resolutionLocation, gl.canvas.width, gl.canvas.height);
    if (viewportLocation !== null) gl.uniform4f(viewportLocation,
      viewport.x / gl.canvas.width,
      viewport.y / gl.canvas.height,
      viewport.width / gl.canvas.width,
      viewport.height / gl.canvas.height
    );
    if (borderColorLocation !== null) gl.uniform3f(borderColorLocation, borderColor[0], borderColor[1], borderColor[2]);
    if (borderWidthLocation !== null) gl.uniform1f(borderWidthLocation, 0.02); // 2% border width

    // Bind texture
    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, texture);

    // Check for WebGL errors
    const error = gl.getError();
    if (error !== gl.NO_ERROR) {
      console.error('WebGL error before draw:', error);
      return;
    }

    // Draw
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);

    // Check for errors after draw
    const drawError = gl.getError();
    if (drawError !== gl.NO_ERROR) {
      console.error('WebGL error after draw:', drawError);
    }
  }, []);

  const calculateVideoViewport = useCallback((
    index: number,
    totalVideos: number,
    canvasWidth: number,
    canvasHeight: number,
    layout: LayoutMode
  ): { x: number; y: number; width: number; height: number } => {
    switch (layout) {
      case 'grid': {
        const cols = Math.ceil(Math.sqrt(totalVideos));
        const rows = Math.ceil(totalVideos / cols);
        const cellWidth = canvasWidth / cols;
        const cellHeight = canvasHeight / rows;
        const col = index % cols;
        const row = Math.floor(index / cols);

        return {
          x: col * cellWidth,
          y: row * cellHeight,
          width: cellWidth,
          height: cellHeight
        };
      }

      case 'focus': {
        if (index === 0) {
          // Main video takes 75% of width
          return {
            x: 0,
            y: 0,
            width: canvasWidth * 0.75,
            height: canvasHeight
          };
        } else {
          // Sidebar videos
          const sidebarWidth = canvasWidth * 0.25;
          const sidebarVideos = totalVideos - 1;
          const itemHeight = sidebarVideos > 0 ? canvasHeight / sidebarVideos : canvasHeight;

          return {
            x: canvasWidth - sidebarWidth,
            y: (index - 1) * itemHeight,
            width: sidebarWidth,
            height: itemHeight
          };
        }
      }

      case 'pip': {
        if (index === 0) {
          // Main video takes full canvas
          return {
            x: 0,
            y: 0,
            width: canvasWidth,
            height: canvasHeight
          };
        } else {
          // Picture-in-picture videos
          const pipSize = 200;
          const margin = 20;

          return {
            x: canvasWidth - pipSize - margin,
            y: margin + (index - 1) * (pipSize + margin),
            width: pipSize,
            height: pipSize
          };
        }
      }

      default:
        return { x: 0, y: 0, width: canvasWidth, height: canvasHeight };
    }
  }, []);

  // Handle local stream
  useEffect(() => {
    if (localStream) {
      if (!localVideoRef.current) {
        const video = document.createElement('video');
        video.autoplay = true;
        video.playsInline = true;
        video.muted = true;
        video.style.position = 'absolute';
        video.style.top = '0px';
        video.style.left = '0px';
        video.style.width = '1px';
        video.style.height = '1px';
        video.style.opacity = '0.01'; // Nearly invisible but still rendered
        video.style.pointerEvents = 'none';
        video.style.zIndex = '-1000';

        // Add to DOM to ensure proper loading
        document.body.appendChild(video);

        localVideoRef.current = video;
        addLog('info', 'Compositor', 'Created local video element for compositor');
      }

      if (localVideoRef.current.srcObject !== localStream) {
        const video = localVideoRef.current;
        video.srcObject = localStream;

        // Force video to load and play
        video.load();
        video.play().then(() => {
          addLog('success', 'Compositor', 'Local video started playing');
        }).catch(error => {
          addLog('error', 'Compositor', 'Failed to play local video', error);
        });

        addLog('success', 'Compositor', 'Assigned local stream to compositor video element');
      }
    } else {
      if (localVideoRef.current) {
        if (localVideoRef.current.parentNode) {
          localVideoRef.current.parentNode.removeChild(localVideoRef.current);
        }
        localVideoRef.current.srcObject = null;
        localVideoRef.current = null;
        addLog('info', 'Compositor', 'Removed local video element from compositor');
      }
    }
  }, [localStream, addLog]);

  // Handle peer streams
  useEffect(() => {
    const peerCount = Object.keys(peers).length;
    const peersWithStreams = Object.values(peers).filter(peer => peer.stream).length;

    // Always log when peers change
    addLog('info', 'Compositor', `Peers updated: ${peerCount} total, ${peersWithStreams} with streams`, {
      peerIds: Object.keys(peers),
      peersWithStreams: Object.entries(peers)
        .filter(([_, peer]) => peer.stream)
        .map(([id, _]) => id)
    });

    // Only log occasionally to avoid spam, but always log when peer count changes
    const shouldLog = Math.random() < 0.1 || peerCount !== Object.keys(videoRefs.current).length;

    if (shouldLog) {
      addLog('debug', 'Compositor', `Processing ${peerCount} peers for compositor (${peersWithStreams} have streams)`, {
        peers: Object.entries(peers).map(([id, peer]) => ({
          id,
          hasStream: !!peer.stream,
          streamId: peer.stream?.id,
          trackCount: peer.stream?.getTracks().length || 0,
          connectionState: peer.connection?.connectionState || 'unknown'
        }))
      });
    }

    // Create video elements for each peer
    Object.entries(peers).forEach(([peerId, peer]) => {
      if (!videoRefs.current[peerId]) {
        const video = document.createElement('video');
        video.autoplay = true;
        video.playsInline = true;
        video.muted = true;
        video.style.position = 'absolute';
        video.style.top = '0px';
        video.style.left = '0px';
        video.style.width = '1px';
        video.style.height = '1px';
        video.style.opacity = '0.01'; // Nearly invisible but still rendered
        video.style.pointerEvents = 'none';
        video.style.zIndex = '-1000';

        // Add to DOM to ensure proper loading
        document.body.appendChild(video);

        videoRefs.current[peerId] = video;
        addLog('info', 'Compositor', `Created video element for peer ${peerId}`);
      }

      // Update stream if it exists and is different
      if (peer.stream && videoRefs.current[peerId].srcObject !== peer.stream) {
        // Check if stream is active
        if (!peer.stream.active) {
          addLog('warn', 'Compositor', `Stream for peer ${peerId} is not active`, {
            streamId: peer.stream.id,
            active: peer.stream.active,
            tracks: peer.stream.getTracks().map((t: MediaStreamTrack) => ({
              kind: t.kind,
              enabled: t.enabled,
              readyState: t.readyState
            }))
          });
        }
        const video = videoRefs.current[peerId];
        video.srcObject = peer.stream;

        // Add comprehensive event listeners to track video loading
        video.addEventListener('loadstart', () => {
          addLog('info', 'Compositor', `Video load started for peer ${peerId}`);
        });

        video.addEventListener('loadedmetadata', () => {
          addLog('success', 'Compositor', `Video metadata loaded for peer ${peerId}`, {
            dimensions: `${video.videoWidth}x${video.videoHeight}`,
            duration: video.duration,
            readyState: video.readyState
          });
        });

        video.addEventListener('loadeddata', () => {
          addLog('success', 'Compositor', `Video data loaded for peer ${peerId}`, {
            readyState: video.readyState
          });
        });

        video.addEventListener('canplay', () => {
          addLog('success', 'Compositor', `Video can play for peer ${peerId}`, {
            readyState: video.readyState
          });
        });

        video.addEventListener('canplaythrough', () => {
          addLog('success', 'Compositor', `Video can play through for peer ${peerId}`, {
            readyState: video.readyState
          });
        });

        video.addEventListener('error', (e) => {
          addLog('error', 'Compositor', `Video error for peer ${peerId}`, {
            error: e,
            networkState: video.networkState,
            readyState: video.readyState
          });
        });

        video.addEventListener('stalled', () => {
          addLog('warn', 'Compositor', `Video stalled for peer ${peerId}`, {
            networkState: video.networkState,
            readyState: video.readyState
          });
        });

        video.addEventListener('waiting', () => {
          addLog('warn', 'Compositor', `Video waiting for peer ${peerId}`, {
            networkState: video.networkState,
            readyState: video.readyState
          });
        });

        // Force video to load and play
        video.load();
        video.play().then(() => {
          addLog('success', 'Compositor', `Video started playing for peer ${peerId}`);
        }).catch(error => {
          addLog('error', 'Compositor', `Failed to play video for peer ${peerId}`, error);
        });

        // Set up a timeout to check if video loads within 5 seconds
        setTimeout(() => {
          if (video.readyState === 0) {
            addLog('warn', 'Compositor', `Video still not loading after 5s for peer ${peerId}, attempting reload`, {
              readyState: video.readyState,
              networkState: video.networkState,
              hasStream: !!video.srcObject,
              streamActive: video.srcObject ? (video.srcObject as MediaStream).active : false
            });

            // Try reassigning the stream
            if (peer.stream) {
              video.srcObject = null;
              setTimeout(() => {
                video.srcObject = peer.stream!;
                video.load();
                video.play().catch(e => {
                  addLog('error', 'Compositor', `Retry play failed for peer ${peerId}`, e);
                });
              }, 100);
            }
          }
        }, 5000);

        addLog('success', 'Compositor', `Assigned stream to video element for peer ${peerId}`, {
          streamId: peer.stream.id,
          tracks: peer.stream.getTracks().map((t: MediaStreamTrack) => ({
            kind: t.kind,
            id: t.id,
            enabled: t.enabled,
            readyState: t.readyState,
            muted: t.muted
          }))
        });
      } else if (!peer.stream) {
        addLog('warn', 'Compositor', `Peer ${peerId} has no stream`);
      } else if (peer.stream && videoRefs.current[peerId].srcObject === peer.stream) {
        // Stream is already assigned, check video state occasionally
        if (Math.random() < 0.01) {
          const video = videoRefs.current[peerId];
          addLog('debug', 'Compositor', `Video state for peer ${peerId}`, {
            readyState: video.readyState,
            dimensions: `${video.videoWidth}x${video.videoHeight}`,
            paused: video.paused,
            ended: video.ended
          });
        }
      }
    });

    // Cleanup removed peers
    Object.keys(videoRefs.current).forEach(peerId => {
      if (!peers[peerId]) {
        const video = videoRefs.current[peerId];
        if (video && video.parentNode) {
          video.parentNode.removeChild(video);
        }
        delete videoRefs.current[peerId];
        addLog('info', 'Compositor', `Removed video element for peer ${peerId}`);
      }
    });
  }, [peers, addLog]);

  // Initialize WebGL on mount
  useEffect(() => {
    if (!initWebGL()) {
      addLog('error', 'WebGL', 'Failed to initialize WebGL, falling back to 2D canvas');
      return;
    }
  }, [initWebGL, addLog]);

  // Main rendering loop
  useEffect(() => {
    const canvas = canvasRef.current;
    const gl = glRef.current;
    const program = programRef.current;

    if (!canvas || !gl || !program) return;

    const draw = () => {
      // Clear the canvas
      gl.clearColor(0.0, 0.0, 0.0, 1.0);
      gl.clear(gl.COLOR_BUFFER_BIT);

      // Collect all available videos (local + peers)
      const allVideos: HTMLVideoElement[] = [];

      // Add local video if available
      if (localVideoRef.current && localVideoRef.current.readyState >= localVideoRef.current.HAVE_CURRENT_DATA) {
        allVideos.push(localVideoRef.current);
      }

      // Add peer videos with better readiness check
      const peerVideos = Object.values(videoRefs.current).filter(video =>
        video.readyState >= video.HAVE_CURRENT_DATA &&
        video.videoWidth > 0 &&
        video.videoHeight > 0
      );
      allVideos.push(...peerVideos);

      // Log occasionally to track video rendering
      if (Math.random() < 0.02) { // Log ~2% of frames for debugging
        const localVideo = localVideoRef.current;
        addLog('debug', 'Compositor', `Drawing ${allVideos.length} videos (${localVideo && localVideo.readyState >= localVideo.HAVE_CURRENT_DATA ? 1 : 0} local + ${peerVideos.length} peers)`, {
          localVideoReady: localVideo ? localVideo.readyState >= localVideo.HAVE_CURRENT_DATA : false,
          localVideoDimensions: localVideo ? `${localVideo.videoWidth}x${localVideo.videoHeight}` : 'none',
          peerVideoDetails: Object.entries(videoRefs.current).map(([peerId, video]) => ({
            peerId,
            readyState: video.readyState,
            dimensions: `${video.videoWidth}x${video.videoHeight}`,
            hasStream: !!video.srcObject
          }))
        });
      }

      if (allVideos.length === 0) {
        // Draw placeholder text
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.fillStyle = '#374151';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.fillStyle = '#9CA3AF';
          ctx.font = '48px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText('Waiting for video streams...', canvas.width / 2, canvas.height / 2);
        }
        return;
      }

      // Update textures and render videos
      allVideos.forEach((video, index) => {
        // Determine video ID more reliably
        let videoId = 'unknown';
        if (video === localVideoRef.current) {
          videoId = 'local';
        } else {
          // Find the peer ID for this video element
          for (const [peerId, peerVideo] of Object.entries(videoRefs.current)) {
            if (peerVideo === video) {
              videoId = peerId;
              break;
            }
          }
        }

        // Log video processing details occasionally
        if (Math.random() < 0.01) {
          addLog('debug', 'WebGL', `Processing video ${index}: ${videoId} (readyState: ${video.readyState}, dimensions: ${video.videoWidth}x${video.videoHeight})`);
        }

        // Get or create texture for this video
        let texture = texturesRef.current.get(videoId);
        if (!texture) {
          const newTexture = createVideoTexture(gl, video);
          if (newTexture) {
            texturesRef.current.set(videoId, newTexture);
            texture = newTexture;
            addLog('success', 'WebGL', `Created texture for video ${videoId}`);
          } else {
            addLog('error', 'WebGL', `Failed to create texture for video ${videoId}`);
            return;
          }
        }

        if (texture && video.videoWidth > 0 && video.videoHeight > 0) {
          try {
            // Update texture with current video frame
            updateVideoTexture(gl, texture, video);

            // Calculate viewport based on layout
            const viewport = calculateVideoViewport(index, allVideos.length, canvas.width, canvas.height, layoutMode);

            // Render the video
            const borderColor: [number, number, number] = video === localVideoRef.current ? [0, 0.83, 1] : [0.54, 0.31, 0.96]; // Blue for local, purple for peers
            renderVideo(gl, program, texture, viewport, borderColor);

            // Log rendering success occasionally
            if (Math.random() < 0.01) {
              addLog('debug', 'WebGL', `Rendered video ${videoId} at viewport (${viewport.x}, ${viewport.y}, ${viewport.width}, ${viewport.height})`);
            }
          } catch (error) {
            addLog('error', 'WebGL', `Failed to render video ${videoId}`, error);
          }
        } else {
          // Log readiness issues more frequently for debugging
          if (Math.random() < 0.05) {
            addLog('warn', 'WebGL', `Video ${videoId} not ready for rendering (readyState: ${video.readyState}, dimensions: ${video.videoWidth}x${video.videoHeight}, hasTexture: ${!!texture})`);
          }
        }
      });
    };

    const animate = () => {
      draw();
      requestAnimationFrame(animate);
    };

    animate();

    // Cleanup function
    return () => {
      // Clean up WebGL textures
      texturesRef.current.forEach((texture) => {
        if (gl && texture) {
          gl.deleteTexture(texture);
        }
      });
      texturesRef.current.clear();
    };
  }, [layoutMode, addLog, calculateVideoViewport, createVideoTexture, updateVideoTexture, renderVideo]);

  return (
    <div className="relative bg-gray-900 rounded-xl overflow-hidden">
      {/* Layout Controls */}
      <div className="absolute top-4 right-4 z-10 flex space-x-2">
        <button
          onClick={() => setLayoutMode('grid')}
          className={`p-2 rounded-lg transition-colors ${
            layoutMode === 'grid' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
          }`}
          title="Grid Layout"
        >
          <Grid className="w-5 h-5" />
        </button>
        <button
          onClick={() => setLayoutMode('focus')}
          className={`p-2 rounded-lg transition-colors ${
            layoutMode === 'focus' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
          }`}
          title="Focus Layout"
        >
          <Monitor className="w-5 h-5" />
        </button>
        <button
          onClick={() => setLayoutMode('pip')}
          className={`p-2 rounded-lg transition-colors ${
            layoutMode === 'pip' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
          }`}
          title="Picture-in-Picture"
        >
          <Maximize2 className="w-5 h-5" />
        </button>
        <button
          onClick={debugVideoState}
          className="p-2 rounded-lg transition-colors bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30"
          title="Debug Video State"
        >
          <Bug className="w-5 h-5" />
        </button>
      </div>

      {/* Composite Canvas */}
      <canvas
        ref={canvasRef}
        width={1920}
        height={1080}
        className="w-full aspect-video"
      />

      {/* Status Overlay */}
      <div className="absolute bottom-4 left-4 bg-gray-800/80 backdrop-blur-sm rounded-lg px-3 py-2">
        <span className="text-white text-sm">
          Layout: {layoutMode.toUpperCase()} • {Object.keys(peers).length} participants • {localStream ? 'Host' : 'No host'}
        </span>
      </div>
    </div>
  );
};