import React, { useState } from 'react';
import { Video, Users, Podcast as Broadcast, ArrowRight } from 'lucide-react';

interface WelcomeScreenProps {
  onJoinAsHost: (roomId: string) => void;
  onJoinAsParticipant: (roomId: string) => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onJoinAsHost,
  onJoinAsParticipant,
}) => {
  const [roomId, setRoomId] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const generateRoomId = () => {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  };

  const handleCreateRoom = () => {
    const newRoomId = generateRoomId();
    setIsCreating(true);
    setTimeout(() => {
      onJoinAsHost(newRoomId);
    }, 500);
  };

  const handleJoinRoom = () => {
    if (roomId.trim()) {
      onJoinAsParticipant(roomId.trim().toUpperCase());
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-2xl">
              <Broadcast className="w-12 h-12 text-white" />
            </div>
          </div>
          <h1 className="text-5xl font-bold text-white mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            StreamForge
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Professional multi-peer WebRTC streaming with real-time compositing and RTMP output
          </p>
        </div>

        {/* Action Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* Host Card */}
          <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-8 hover:bg-gray-800/70 transition-all duration-300 group">
            <div className="flex items-center mb-6">
              <div className="bg-blue-500/20 p-3 rounded-lg mr-4 group-hover:bg-blue-500/30 transition-colors">
                <Video className="w-8 h-8 text-blue-400" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-white">Host Stream</h3>
                <p className="text-gray-300">Composite multiple feeds</p>
              </div>
            </div>
            <ul className="text-gray-300 space-y-2 mb-8">
              <li>• WebGL video compositing</li>
              <li>• Real-time audio mixing</li>
              <li>• RTMP streaming output</li>
              <li>• Connection management</li>
            </ul>
            <button
              onClick={handleCreateRoom}
              disabled={isCreating}
              className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50"
            >
              {isCreating ? (
                <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <>
                  <span>Create Room</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>

          {/* Participant Card */}
          <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-8 hover:bg-gray-800/70 transition-all duration-300 group">
            <div className="flex items-center mb-6">
              <div className="bg-purple-500/20 p-3 rounded-lg mr-4 group-hover:bg-purple-500/30 transition-colors">
                <Users className="w-8 h-8 text-purple-400" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-white">Join Stream</h3>
                <p className="text-gray-300">Connect as participant</p>
              </div>
            </div>
            <ul className="text-gray-300 space-y-2 mb-8">
              <li>• Share camera & microphone</li>
              <li>• Real-time video streaming</li>
              <li>• Low-latency WebRTC</li>
              <li>• Quality indicators</li>
            </ul>
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Enter Room ID"
                value={roomId}
                onChange={(e) => setRoomId(e.target.value.toUpperCase())}
                className="w-full bg-gray-700/50 border border-gray-600 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                maxLength={6}
              />
              <button
                onClick={handleJoinRoom}
                disabled={!roomId.trim()}
                className="w-full bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span>Join Room</span>
                <ArrowRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="bg-gray-800/30 backdrop-blur-lg border border-gray-700 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-white mb-6 text-center">Platform Features</h3>
          <div className="grid md:grid-cols-3 gap-6 text-center">
            <div>
              <div className="bg-green-500/20 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3">
                <div className="w-6 h-6 bg-green-400 rounded-full"></div>
              </div>
              <h4 className="text-white font-semibold mb-2">WebRTC Native</h4>
              <p className="text-gray-300 text-sm">Ultra-low latency peer-to-peer connections</p>
            </div>
            <div>
              <div className="bg-yellow-500/20 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3">
                <div className="w-6 h-6 bg-yellow-400 rounded-full"></div>
              </div>
              <h4 className="text-white font-semibold mb-2">WebGL Compositing</h4>
              <p className="text-gray-300 text-sm">Hardware-accelerated video processing</p>
            </div>
            <div>
              <div className="bg-red-500/20 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3">
                <div className="w-6 h-6 bg-red-400 rounded-full"></div>
              </div>
              <h4 className="text-white font-semibold mb-2">RTMP Output</h4>
              <p className="text-gray-300 text-sm">Stream to any RTMP-compatible platform</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};