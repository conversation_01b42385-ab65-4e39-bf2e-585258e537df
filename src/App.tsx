import React, { useState, useEffect } from 'react';
import { WelcomeScreen } from './components/WelcomeScreen';
import { HostInterface } from './components/HostInterface';
import { ParticipantInterface } from './components/ParticipantInterface';
import { LogConsole } from './components/LogConsole';
import { ConnectionProvider } from './contexts/ConnectionContext';
import { LogProvider, useLog } from './contexts/LogContext';

type AppMode = 'welcome' | 'host' | 'participant';

const AppContent: React.FC = () => {
  const [mode, setMode] = useState<AppMode>('welcome');
  const [roomId, setRoomId] = useState<string>('');
  const { logs, clearLogs } = useLog();

  const handleJoinAsHost = (id: string) => {
    setRoomId(id);
    setMode('host');
  };

  const handleJoinAsParticipant = (id: string) => {
    setRoomId(id);
    setMode('participant');
  };

  return (
    <ConnectionProvider>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
        {mode === 'welcome' && (
          <WelcomeScreen
            onJoinAsHost={handleJoinAsHost}
            onJoinAsParticipant={handleJoinAsParticipant}
          />
        )}
        {mode === 'host' && (
          <HostInterface roomId={roomId} onLeave={() => setMode('welcome')} />
        )}
        {mode === 'participant' && (
          <ParticipantInterface roomId={roomId} onLeave={() => setMode('welcome')} />
        )}

        {/* Log Console - only show when not on welcome screen */}
        {mode !== 'welcome' && (
          <LogConsole logs={logs} onClear={clearLogs} />
        )}
      </div>
    </ConnectionProvider>
  );
};

function App() {
  return (
    <LogProvider>
      <AppContent />
    </LogProvider>
  );
}

export default App;