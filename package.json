{"name": "webrtc-streaming-app", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "vite", "dev:server": "nodemon server/index.js", "build": "vite build", "build:serve": "npm run build && npm run serve", "serve": "node server/index.js", "start": "npm run build:serve", "deploy": "./deploy.sh", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "socket.io-client": "^4.7.4"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "cors": "^2.8.5", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "globals": "^15.9.0", "node-pre-gyp": "^0.17.0", "nodemon": "^3.0.2", "postcss": "^8.4.35", "socket.io": "^4.7.4", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "wrtc": "^0.4.7"}}